import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import VueAMap from 'vue-amap';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css'; // 引入Element UI样式
import './assets/font/font.css'

Vue.config.productionTip = false

// 高德地图配置
Vue.use(VueAMap); // 使用插件
VueAMap.initAMapApiLoader({ // 初始化插件
  key: "e18de717bc0db5e1faa219d669d00e05", // 高德key，自己官网申请即可，这里是我申请的
  plugin: ["AMap.Geocoder"],  // 插件集合，这里只有一个定位功能，所以就只放一个AMap.Geocoder
  uiVersion: "1.0.11", // 不加会报错，加上吧
  v: "1.4.15", // 不加也不会影响，顺手加上吧
});

//全局引入echarts
import * as echarts from 'echarts';
//需要挂载到Vue原型上
Vue.prototype.$echarts = echarts

import 'lib-flexible/flexible'

// 引用axios
import axios from 'axios'


// axios.defaults.baseURL = 'https://mi.medodt.com/';  //其他项目域名
axios.defaults.baseURL = '/api'; //代理接口


axios.interceptors.response.use(res => {
  return res.data;
})

//判断缓存

// import 'lib-flexible/flexible.js'

Vue.use(ElementUI, VueAMap)

new Vue({
  router,
  store,
  render: h => h(App),
  // created() {
  //   alert("请输入账号密码")
  //   this.$router.push('/caseList'); // 默认打开About页面
  // },
  beforeCreate() {
    Vue.prototype.$axios = axios
    Vue.prototype.$dbName = `6haofuwu`
  }
}).$mount('#app')


